import base64
import json
from DatabaseConnection import DatabaseConnection
from CacheManager import CacheManager
from CacheMonitor import CacheMonitor

def fetch_and_cache_service_zones(limit, skip):
    """
    Fetch service zones from MongoDB and cache them using CacheManager and CacheMonitor
    """
    cache_monitor = None
    try:
        # Initialize cache monitor for performance tracking
        cache_monitor = CacheMonitor()
        print("Cache monitor initialized")

        # Initialize database connection
        db_connection = DatabaseConnection()
        print("Database connection established")

        # Get service zones collection
        service_zones_collection = db_connection.get_collection('servicezone_h3map')

        # Fetch all service zone H3 mappings
        h3_mappings = list(service_zones_collection.find({}).skip(skip).limit(limit))
        print(f"Fetched {len(h3_mappings)} H3 mappings from database")

        # Initialize cache manager
        cache_manager = CacheManager()

        # Test cache connection
        if not cache_manager.testConnection():
            print("Failed to connect to cache")
            cache_monitor.log_cache_error("connection", "Failed to connect to cache server")
            return False

        # Cache each H3 mapping with _id as key and fips as value
        cached_count = 0
        ttl = 3600  # 1 hour TTL

        for mapping in h3_mappings:
            h3_id = mapping.get('_id')
            fips = mapping.get('fips', [])

            if h3_id:
                # Use the H3 ID directly as the cache key
                cache_key = str(h3_id)

                # Cache the fips array as the value
                if cache_manager.setJson(cache_key, fips, ttl):
                    cache_monitor.log_cache_set(cache_key)
                    cached_count += 1
                else:
                    cache_monitor.log_cache_error("set", f"Failed to cache H3 mapping {h3_id}")
            else:
                print("Found mapping without _id field")

        print(f"Successfully cached {cached_count} H3 mappings")

        # Print cache performance report
        cache_monitor.print_performance_report()

        # Perform health check
        health_status = cache_monitor.check_cache_health()
        if health_status['healthy']:
            print("Cache health check: HEALTHY")
        else:
            print(f"Cache health issues: {health_status['issues']}")

        return True

    except Exception as e:
        if cache_monitor:
            cache_monitor.log_cache_error("general", str(e))
        print(f"Error in fetch_and_cache_service_zones: {str(e)}")
        return False
    finally:
        # Close database connection
        try:
            db_connection.close_connection()
            print("Database connection closed")
        except:
            pass

def get_cached_h3_mapping(h3_id):
    """
    Retrieve cached FIPS data for a specific H3 ID
    """
    cache_monitor = None
    try:
        # Initialize cache monitor and manager
        cache_monitor = CacheMonitor()
        cache_manager = CacheManager()

        # Test cache connection
        if not cache_manager.testConnection():
            print("Failed to connect to cache for retrieval")
            return None

        # Try to get FIPS data for the H3 ID
        cache_key = str(h3_id)
        cached_fips = cache_manager.getJson(cache_key)

        if cached_fips is not None:
            cache_monitor.log_cache_hit(cache_key)
            print(f"Cache HIT: Retrieved FIPS data for H3 ID {h3_id}: {cached_fips}")
            return cached_fips
        else:
            cache_monitor.log_cache_miss(cache_key)
            print(f"Cache MISS: No FIPS data found for H3 ID {h3_id}")
            return None

    except Exception as e:
        if cache_monitor:
            cache_monitor.log_cache_error("get", str(e))
        print(f"Error retrieving cached H3 mapping for {h3_id}: {str(e)}")
        return None

def test_cache_retrieval():
    """
    Test cache retrieval with sample H3 IDs
    """
    # Test with a sample H3 ID (you can replace with actual IDs from your data)
    sample_h3_ids = ["87260e535ffffff"]  # Add more sample IDs as needed

    results = {}
    for h3_id in sample_h3_ids:
        fips_data = get_cached_h3_mapping(h3_id)
        results[h3_id] = fips_data

    return results

def lambda_handler(event, context):
    """
    Lambda handler that processes Kafka events and caches service zones
    """
    print("Lambda function started")

    try:
        # Process Kafka event if present
        if 'records' in event:
            print("Processing Kafka event")
            for topic_partition in event['records'].keys():
                records = event['records'][topic_partition]
                for record in records:
                    payload = base64.b64decode(record['value']).decode('utf-8')
                    print(f"From {topic_partition}: {payload}")

                    # Parse the payload to check if it's a cache refresh request
                    try:
                        payload_data = json.loads(payload)
                        if payload_data.get('action') == 'refresh_cache' or 'ttl' in payload_data:
                            limit = payload_data.get('chunk_size', 0)
                            skip = int(payload_data.get('chunk_index', 0)) * int(limit)
                            print("Cache refresh requested via Kafka event", limit, skip)
                            fetch_and_cache_service_zones(limit, skip)
                    except json.JSONDecodeError:
                        print(f"Could not parse payload as JSON: {payload}")

        # Always perform cache refresh (can be modified based on requirements)
        cache_success = fetch_and_cache_service_zones()

        # Demonstrate cache retrieval with test H3 mappings
        test_results = test_cache_retrieval()
        cached_mappings_count = len([v for v in test_results.values() if v is not None])

        if cache_success:
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Successfully processed event and cached H3 mappings',
                    'cached': True,
                    'test_h3_mappings': test_results,
                    'cached_mappings_found': cached_mappings_count,
                    'cache_retrieval_test': 'success' if cached_mappings_count > 0 else 'no_data_found'
                })
            }
        else:
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'message': 'Event processed but caching failed',
                    'cached': False,
                    'test_h3_mappings': test_results,
                    'cached_mappings_found': cached_mappings_count
                })
            }

    except Exception as e:
        print(f"Error in lambda_handler: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': f'Error processing event: {str(e)}',
                'cached': False
            })
        }


